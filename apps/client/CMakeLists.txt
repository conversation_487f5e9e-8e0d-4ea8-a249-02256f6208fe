# <PERSON><PERSON><PERSON> bảo CMake version hiện đại
cmake_minimum_required(VERSION 3.18)

# Đặt tên project cho app này
project(client_app VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Qt5 dependencies are handled centrally in cmake/dependencies.cmake
# Check if Qt5 is available before proceeding
if(NOT Qt5_FOUND)
    if(ORANGE_PI_TARGET OR RASPBERRY_PI_TARGET)
        message(WARNING "Qt5 not found for cross-compilation. Skipping client application.")
        return()
    else()
        message(FATAL_ERROR "Qt5 is required for client application but was not found")
    endif()
endif()

# Create executable with working enhanced version
add_executable(client_app
    src/working_enhanced_main.cpp
)

# Create new single stream executable with face recognition UI
add_executable(single_stream_client
    src/single_stream_main.cpp
    src/ui/single_stream_main_window.cpp
    src/widgets/single_stream_display.cpp
    src/widgets/face_recognition_sidebar.cpp
)

# Create simple face recognition demo (no Q_OBJECT issues)
add_executable(face_recognition_demo
    src/simple_face_recognition_demo.cpp
)

# Add include directories
target_include_directories(client_app PRIVATE
    include
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

target_include_directories(single_stream_client PRIVATE
    include
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

target_include_directories(face_recognition_demo PRIVATE
    include
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Enable Qt MOC processing
set_target_properties(client_app PROPERTIES
    AUTOMOC ON
    AUTOUIC ON
    AUTORCC ON
)

set_target_properties(single_stream_client PROPERTIES
    AUTOMOC ON
    AUTOUIC ON
    AUTORCC ON
)

set_target_properties(face_recognition_demo PROPERTIES
    AUTOMOC ON
    AUTOUIC ON
    AUTORCC ON
)

# Find additional Qt5 components
find_package(Qt5 REQUIRED COMPONENTS Network)

# Link with Qt5 libraries using centralized function
link_qt5_dependencies(client_app)
link_qt5_dependencies(single_stream_client)
link_qt5_dependencies(face_recognition_demo)

# Link additional Qt5 components
target_link_libraries(client_app PRIVATE
    Qt5::Network
)

target_link_libraries(single_stream_client PRIVATE
    Qt5::Network
)

target_link_libraries(face_recognition_demo PRIVATE
    Qt5::Network
)

# Link with shared library
target_link_libraries(client_app PRIVATE shared)
target_link_libraries(single_stream_client PRIVATE shared)
target_link_libraries(face_recognition_demo PRIVATE shared)

# Link with OpenSSL if available (required by shared library)
if(USE_OPENSSL)
    target_link_libraries(client_app PRIVATE OpenSSL::SSL OpenSSL::Crypto)
    target_link_libraries(single_stream_client PRIVATE OpenSSL::SSL OpenSSL::Crypto)
    target_link_libraries(face_recognition_demo PRIVATE OpenSSL::SSL OpenSSL::Crypto)
endif()

# Compile options
target_compile_options(client_app PRIVATE
    -Wall -Wextra -Wpedantic
    $<$<CONFIG:Debug>:-g -O0>
    $<$<CONFIG:Release>:-O3 -DNDEBUG>
)

target_compile_options(single_stream_client PRIVATE
    -Wall -Wextra -Wpedantic
    $<$<CONFIG:Debug>:-g -O0>
    $<$<CONFIG:Release>:-O3 -DNDEBUG>
)

target_compile_options(face_recognition_demo PRIVATE
    -Wall -Wextra -Wpedantic
    $<$<CONFIG:Debug>:-g -O0>
    $<$<CONFIG:Release>:-O3 -DNDEBUG>
)

# Resources are handled by Qt's resource system (QRC files)
# No need to copy assets directory as it doesn't exist

# Install targets
install(TARGETS client_app single_stream_client face_recognition_demo DESTINATION bin)

# Copy .env file to build directory
configure_file(${CMAKE_SOURCE_DIR}/.env ${CMAKE_BINARY_DIR}/apps/client/.env COPYONLY)
