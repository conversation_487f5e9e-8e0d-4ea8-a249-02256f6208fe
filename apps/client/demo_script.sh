#!/bin/bash

# C-AIBOX Enhanced Client Demo Script
# This script demonstrates the enhanced RTSP client functionality

echo "=================================================="
echo "C-AIBOX Enhanced RTSP Client Demo"
echo "=================================================="
echo ""

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

echo "🔧 Building the enhanced client..."
echo ""

# Create build directory if it doesn't exist
if [ ! -d "build" ]; then
    mkdir build
fi

cd build

# Configure and build
echo "📋 Configuring CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Debug

echo ""
echo "🔨 Building client application..."
make client_app -j4

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Build successful!"
    echo ""
    
    echo "🚀 Enhanced Client Features:"
    echo "   • Real RTSP stream support"
    echo "   • Multi-stream grid layout (1-6 streams per row)"
    echo "   • Face detection overlays with bounding boxes"
    echo "   • Modern dark theme UI"
    echo "   • Configuration dialog for settings"
    echo "   • Stream management (add/remove/configure)"
    echo "   • Network communication with server"
    echo "   • Persistent settings storage"
    echo ""
    
    echo "📺 Example RTSP URLs supported:"
    echo "   rtsp://admin:CMC2024!@192.168.222.169:554/streaming/channels/01"
    echo "   rtsp://user:password@*************:554/stream1"
    echo "   rtsp://camera.local:554/live/main"
    echo ""
    
    echo "🎮 How to use the application:"
    echo "   1. Run: ./apps/client/client_app"
    echo "   2. Use 'Configuration' to set server settings"
    echo "   3. Use 'Add Stream' to add RTSP camera streams"
    echo "   4. Click 'Info' on any stream for details"
    echo "   5. Use 'Test Connection' to verify server connectivity"
    echo ""
    
    echo "🔧 Configuration options:"
    echo "   • Server Address: IP of C-AIBOX server"
    echo "   • Server Port: Communication port (default: 8080)"
    echo "   • Max Streams per Row: Grid layout (1-6)"
    echo "   • Show Overlays: Enable/disable face detection"
    echo ""
    
    echo "📁 Key files created:"
    echo "   • apps/client/src/working_enhanced_main.cpp (main application)"
    echo "   • apps/client/ENHANCED_README.md (documentation)"
    echo "   • ENHANCED_CLIENT_IMPLEMENTATION.md (summary)"
    echo ""
    
    echo "🎯 Ready to run! Execute:"
    echo "   cd build && ./apps/client/client_app"
    echo ""
    
    # Check if display is available
    if [ -n "$DISPLAY" ]; then
        echo "🖥️  Display detected. You can run the GUI application."
        echo ""
        read -p "Would you like to start the application now? (y/n): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "🚀 Starting C-AIBOX Enhanced Client..."
            ./apps/client/client_app &
            echo "✅ Application started in background (PID: $!)"
            echo "   The GUI window should appear shortly."
        fi
    else
        echo "ℹ️  No display detected. GUI application requires X11 forwarding or local display."
        echo "   To run with X11 forwarding: ssh -X user@host"
    fi
    
else
    echo ""
    echo "❌ Build failed! Please check the error messages above."
    exit 1
fi

echo ""
echo "=================================================="
echo "Demo completed successfully!"
echo "=================================================="
