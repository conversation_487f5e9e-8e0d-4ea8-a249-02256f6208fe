#include <QApplication>
#include <QMainWindow>
#include <QWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QLabel>
#include <QFrame>
#include <QListWidget>
#include <QListWidgetItem>
#include <QPainter>
#include <QTimer>
#include <QDateTime>
#include <QRandomGenerator>
#include <QFont>
#include <QPixmap>
#include <QtMath>
#include <QDebug>

/**
 * @brief Simple face result data structure
 */
struct SimpleFaceResult {
    QString name;
    QString confidence;
    QString timestamp;
    QString status;
    
    SimpleFaceResult(const QString& n, const QString& c, const QString& s = "Cho phép")
        : name(n), confidence(c), status(s), timestamp(QDateTime::currentDateTime().toString("hh:mm:ss")) {}
};

/**
 * @brief Simple face result widget for display
 */
class SimpleFaceResultWidget : public QWidget
{
public:
    explicit SimpleFaceResultWidget(const SimpleFaceResult& result, QWidget* parent = nullptr)
        : QWidget(parent), m_result(result)
    {
        setFixedHeight(80);
        setMinimumWidth(300);
    }

protected:
    void paintEvent(QPaintEvent* event) override
    {
        Q_UNUSED(event)
        
        QPainter painter(this);
        painter.setRenderHint(QPainter::Antialiasing);
        
        // Background
        QColor bgColor = (m_result.status == "Cho phép") ? QColor(40, 60, 80) : QColor(80, 40, 40);
        painter.fillRect(rect(), bgColor);
        
        // Border
        QColor borderColor = (m_result.status == "Cho phép") ? QColor(0, 150, 0) : QColor(200, 50, 50);
        painter.setPen(QPen(borderColor, 2));
        painter.drawRect(rect().adjusted(1, 1, -1, -1));
        
        // Avatar placeholder
        painter.setBrush(QColor(100, 150, 200));
        painter.setPen(Qt::NoPen);
        painter.drawEllipse(10, 10, 60, 60);
        
        // Avatar initial
        painter.setPen(Qt::white);
        painter.setFont(QFont("Arial", 20, QFont::Bold));
        QString initial = m_result.name.isEmpty() ? "?" : QString(m_result.name.at(0).toUpper());
        painter.drawText(QRect(10, 10, 60, 60), Qt::AlignCenter, initial);
        
        // Text content
        painter.setPen(Qt::white);
        
        // Name
        QFont nameFont("Arial", 12, QFont::Bold);
        painter.setFont(nameFont);
        painter.drawText(80, 25, m_result.name.isEmpty() ? "Unknown" : m_result.name);
        
        // Confidence
        QFont detailFont("Arial", 10);
        painter.setFont(detailFont);
        painter.setPen(QColor(200, 200, 200));
        painter.drawText(80, 42, QString("Confidence: %1").arg(m_result.confidence));
        
        // Timestamp
        painter.drawText(80, 57, m_result.timestamp);
        
        // Status
        painter.setPen(borderColor);
        QFont statusFont("Arial", 10, QFont::Bold);
        painter.setFont(statusFont);
        painter.drawText(width() - 80, 35, m_result.status);
    }

private:
    SimpleFaceResult m_result;
};

/**
 * @brief Simple face recognition sidebar
 */
class SimpleFaceRecognitionSidebar : public QWidget
{
public:
    explicit SimpleFaceRecognitionSidebar(QWidget* parent = nullptr)
        : QWidget(parent)
    {
        setFixedWidth(350);
        setStyleSheet(R"(
            QWidget {
                background-color: #1e2329;
                border-left: 2px solid #3a4149;
            }
            QLabel {
                color: white;
                font-family: Arial;
            }
            QListWidget {
                background-color: #2a3038;
                border: 1px solid #3a4149;
                border-radius: 5px;
            }
            QListWidget::item {
                border-bottom: 1px solid #3a4149;
                padding: 0px;
            }
        )");
        
        setupUI();
        addMockData();
    }

    void addFaceResult(const SimpleFaceResult& result)
    {
        SimpleFaceResultWidget* resultWidget = new SimpleFaceResultWidget(result);
        
        QListWidgetItem* item = new QListWidgetItem();
        item->setSizeHint(resultWidget->sizeHint());
        
        m_resultsList->insertItem(0, item);
        m_resultsList->setItemWidget(item, resultWidget);
        
        // Limit results
        while (m_resultsList->count() > 50) {
            QListWidgetItem* lastItem = m_resultsList->takeItem(m_resultsList->count() - 1);
            delete lastItem;
        }
        
        updateCount();
    }

private:
    void setupUI()
    {
        QVBoxLayout* layout = new QVBoxLayout(this);
        layout->setContentsMargins(15, 15, 15, 15);
        layout->setSpacing(10);
        
        // Title
        QLabel* titleLabel = new QLabel("DANH SÁCH NHẬN DIỆN");
        titleLabel->setFont(QFont("Arial", 14, QFont::Bold));
        titleLabel->setAlignment(Qt::AlignCenter);
        titleLabel->setStyleSheet("color: white; padding: 10px; background-color: #3a4149; border-radius: 5px;");
        layout->addWidget(titleLabel);
        
        // Count label
        m_countLabel = new QLabel("0 người");
        m_countLabel->setFont(QFont("Arial", 10));
        m_countLabel->setAlignment(Qt::AlignRight);
        m_countLabel->setStyleSheet("color: #888;");
        layout->addWidget(m_countLabel);
        
        // Results list
        m_resultsList = new QListWidget();
        m_resultsList->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel);
        m_resultsList->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
        layout->addWidget(m_resultsList);
    }
    
    void addMockData()
    {
        addFaceResult(SimpleFaceResult("Nguyễn Thái Quốc Huy", "95.5%", "Cho phép"));
        addFaceResult(SimpleFaceResult("Trịnh Quốc Bảo", "90.8%", "Cho phép"));
        addFaceResult(SimpleFaceResult("Trần Thành Long", "89.2%", "Từ chối"));
    }
    
    void updateCount()
    {
        m_countLabel->setText(QString("%1 người").arg(m_resultsList->count()));
    }

    QLabel* m_countLabel;
    QListWidget* m_resultsList;
};

/**
 * @brief Simple stream display widget
 */
class SimpleStreamDisplay : public QFrame
{
public:
    explicit SimpleStreamDisplay(QWidget* parent = nullptr)
        : QFrame(parent), m_frameCounter(0)
    {
        setFrameStyle(QFrame::Box);
        setLineWidth(2);
        setStyleSheet("background-color: #1a1a1a; border: 2px solid #333;");
        setMinimumSize(640, 480);
        
        // Setup timer for animation
        m_timer = new QTimer(this);
        connect(m_timer, &QTimer::timeout, this, [this]() {
            m_frameCounter++;
            update();
        });
        m_timer->start(33); // ~30 FPS
    }

protected:
    void paintEvent(QPaintEvent* event) override
    {
        Q_UNUSED(event)
        
        QPainter painter(this);
        painter.setRenderHint(QPainter::Antialiasing);
        
        // Draw mock video background
        painter.fillRect(rect(), QColor(40, 50, 60));
        
        // Draw animated elements
        int time = m_frameCounter;
        
        // Moving circle
        int circleX = (time * 2) % width();
        int circleY = height() / 2 + static_cast<int>(100 * qSin(time * 0.1));
        painter.setBrush(QColor(100, 150, 255, 100));
        painter.setPen(Qt::NoPen);
        painter.drawEllipse(circleX - 20, circleY - 20, 40, 40);
        
        // Grid pattern
        painter.setPen(QPen(QColor(60, 80, 120), 1));
        for (int x = 0; x < width(); x += 50) {
            painter.drawLine(x, 0, x, height());
        }
        for (int y = 0; y < height(); y += 50) {
            painter.drawLine(0, y, width(), y);
        }
        
        // Draw mock face detection boxes
        drawMockFaces(painter);
        
        // Draw stream info
        drawStreamInfo(painter);
        
        // Draw timestamp
        drawTimestamp(painter);
    }

private:
    void drawMockFaces(QPainter& painter)
    {
        // Draw some mock face detection boxes
        QList<QRect> faceBoxes = {
            QRect(150, 100, 100, 120),
            QRect(400, 200, 90, 110)
        };
        
        QStringList names = {"Nguyễn Thái Quốc Huy", "Trịnh Quốc Bảo"};
        QStringList confidences = {"95.5%", "90.8%"};
        
        for (int i = 0; i < faceBoxes.size(); ++i) {
            QRect box = faceBoxes[i];
            
            // Draw bounding box
            painter.setPen(QPen(QColor(0, 255, 0), 3));
            painter.setBrush(Qt::NoBrush);
            painter.drawRect(box);
            
            // Draw name label
            QString labelText = QString("%1 (%2)").arg(names[i]).arg(confidences[i]);
            
            QFont labelFont("Arial", 12, QFont::Bold);
            painter.setFont(labelFont);
            QFontMetrics fm(labelFont);
            
            QRect textRect = fm.boundingRect(labelText);
            textRect.adjust(-5, -2, 5, 2);
            textRect.moveTopLeft(QPoint(box.x(), box.y() - textRect.height() - 5));
            
            // Draw label background
            painter.setBrush(QColor(0, 0, 0, 180));
            painter.setPen(Qt::NoPen);
            painter.drawRect(textRect);
            
            // Draw label text
            painter.setPen(Qt::white);
            painter.drawText(textRect, Qt::AlignCenter, labelText);
        }
    }
    
    void drawStreamInfo(QPainter& painter)
    {
        painter.setPen(Qt::white);
        painter.setBrush(QColor(0, 0, 0, 150));
        
        QFont infoFont("Arial", 11);
        painter.setFont(infoFont);
        
        QString infoText = "Camera 1 - 192.168.1.101";
        
        QFontMetrics fm(infoFont);
        QRect textRect = fm.boundingRect(infoText);
        textRect.adjust(-10, -5, 10, 5);
        textRect.moveTopLeft(QPoint(10, 10));
        
        painter.drawRect(textRect);
        painter.setPen(Qt::white);
        painter.drawText(textRect, Qt::AlignCenter, infoText);
    }
    
    void drawTimestamp(QPainter& painter)
    {
        QString timestamp = QDateTime::currentDateTime().toString("dd/MM/yyyy hh:mm:ss");
        
        painter.setPen(Qt::white);
        painter.setBrush(QColor(0, 0, 0, 150));
        
        QFont timestampFont("Arial", 12, QFont::Bold);
        painter.setFont(timestampFont);
        
        QFontMetrics fm(timestampFont);
        QRect textRect = fm.boundingRect(timestamp);
        textRect.adjust(-10, -5, 10, 5);
        textRect.moveBottomLeft(QPoint(10, height() - 10));
        
        painter.drawRect(textRect);
        painter.setPen(Qt::white);
        painter.drawText(textRect, Qt::AlignCenter, timestamp);
    }

    QTimer* m_timer;
    int m_frameCounter;
};

/**
 * @brief Simple main window
 */
class SimpleFaceRecognitionMainWindow : public QMainWindow
{
public:
    explicit SimpleFaceRecognitionMainWindow(QWidget* parent = nullptr)
        : QMainWindow(parent)
    {
        setWindowTitle("C-AIBOX - Face Recognition System");
        setMinimumSize(1200, 800);
        resize(1600, 900);
        
        setupUI();
        setupMockDataTimer();
    }

private:
    void setupUI()
    {
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QHBoxLayout* mainLayout = new QHBoxLayout(centralWidget);
        mainLayout->setContentsMargins(0, 0, 0, 0);
        mainLayout->setSpacing(0);
        
        // Create stream display
        m_streamDisplay = new SimpleStreamDisplay();
        
        // Create sidebar
        m_sidebar = new SimpleFaceRecognitionSidebar();
        
        // Add to layout
        mainLayout->addWidget(m_streamDisplay, 1);
        mainLayout->addWidget(m_sidebar, 0);
        
        // Apply dark theme
        setStyleSheet(R"(
            QMainWindow {
                background-color: #2b2b2b;
            }
        )");
    }
    
    void setupMockDataTimer()
    {
        QTimer* mockTimer = new QTimer(this);
        connect(mockTimer, &QTimer::timeout, this, [this]() {
            // Add random mock face results
            QStringList names = {
                "Nguyễn Thái Quốc Huy", 
                "Trịnh Quốc Bảo", 
                "Trần Thành Long", 
                "Lê Văn Nam",
                "Phạm Thị Mai",
                "Unknown Person"
            };
            
            QStringList statuses = {"Cho phép", "Từ chối"};
            
            int count = QRandomGenerator::global()->bounded(0, 2);
            for (int i = 0; i < count; ++i) {
                QString name = names[QRandomGenerator::global()->bounded(names.size())];
                QString confidence = QString("%1%").arg(QRandomGenerator::global()->bounded(75, 99));
                QString status = (name == "Unknown Person") ? "Từ chối" : 
                                statuses[QRandomGenerator::global()->bounded(statuses.size())];
                
                m_sidebar->addFaceResult(SimpleFaceResult(name, confidence, status));
            }
        });
        mockTimer->start(3000); // Add new results every 3 seconds
    }

    SimpleStreamDisplay* m_streamDisplay;
    SimpleFaceRecognitionSidebar* m_sidebar;
};

int main(int argc, char* argv[])
{
    QApplication app(argc, argv);
    
    app.setApplicationName("C-AIBOX Face Recognition Demo");
    app.setApplicationVersion("1.0.0");
    
    SimpleFaceRecognitionMainWindow window;
    window.show();
    
    return app.exec();
}
