#include "widgets/face_recognition_sidebar.hpp"
#include <QApplication>
#include <QPainter>
#include <QFontMetrics>
#include <QDebug>

// FaceResultWidget implementation
FaceResultWidget::FaceResultWidget(const FaceResult& result, QWidget* parent)
    : QWidget(parent), m_result(result)
{
    setFixedHeight(80);
    setMinimumWidth(300);
    updateScaledAvatar();
}

void FaceResultWidget::updateResult(const FaceResult& result)
{
    m_result = result;
    updateScaledAvatar();
    update();
}

void FaceResultWidget::updateScaledAvatar()
{
    if (!m_result.avatar.isNull()) {
        m_scaledAvatar = m_result.avatar.scaled(60, 60, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    } else {
        // Generate default avatar
        m_scaledAvatar = QPixmap(60, 60);
        m_scaledAvatar.fill(QColor(100, 150, 200));
        
        QPainter painter(&m_scaledAvatar);
        painter.setRenderHint(QPainter::Antialiasing);
        painter.setPen(Qt::white);
        painter.setFont(QFont("Arial", 20, QFont::Bold));
        
        QString initials = m_result.name.isEmpty() ? "?" : QString(m_result.name.at(0).toUpper());
        painter.drawText(m_scaledAvatar.rect(), Qt::AlignCenter, initials);
    }
}

void FaceResultWidget::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Background
    QColor bgColor = (m_result.status == "Cho phép") ? QColor(40, 60, 80) : QColor(80, 40, 40);
    painter.fillRect(rect(), bgColor);
    
    // Border
    QColor borderColor = (m_result.status == "Cho phép") ? QColor(0, 150, 0) : QColor(200, 50, 50);
    painter.setPen(QPen(borderColor, 2));
    painter.drawRect(rect().adjusted(1, 1, -1, -1));
    
    // Avatar
    painter.drawPixmap(10, 10, m_scaledAvatar);
    
    // Text content
    painter.setPen(Qt::white);
    
    // Name
    QFont nameFont("Arial", 12, QFont::Bold);
    painter.setFont(nameFont);
    painter.drawText(80, 25, m_result.name.isEmpty() ? "Unknown" : m_result.name);
    
    // Confidence
    QFont detailFont("Arial", 10);
    painter.setFont(detailFont);
    painter.setPen(QColor(200, 200, 200));
    painter.drawText(80, 42, QString("Confidence: %1").arg(m_result.confidence));
    
    // Timestamp
    painter.drawText(80, 57, m_result.timestamp);
    
    // Status
    painter.setPen(borderColor);
    QFont statusFont("Arial", 10, QFont::Bold);
    painter.setFont(statusFont);
    painter.drawText(width() - 80, 35, m_result.status);
}

// FaceRecognitionSidebar implementation
FaceRecognitionSidebar::FaceRecognitionSidebar(QWidget* parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_titleLabel(nullptr)
    , m_countLabel(nullptr)
    , m_resultsList(nullptr)
    , m_maxResults(DEFAULT_MAX_RESULTS)
    , m_showTimestamps(true)
    , m_showConfidence(true)
    , m_cleanupTimer(nullptr)
{
    setupUI();
    
    // Setup cleanup timer
    m_cleanupTimer = new QTimer(this);
    connect(m_cleanupTimer, &QTimer::timeout, this, &FaceRecognitionSidebar::onCleanupOldResults);
    m_cleanupTimer->start(CLEANUP_INTERVAL_MS);
}

FaceRecognitionSidebar::~FaceRecognitionSidebar()
{
    if (m_cleanupTimer) {
        m_cleanupTimer->stop();
    }
}

void FaceRecognitionSidebar::setupUI()
{
    setFixedWidth(350);
    setStyleSheet(R"(
        FaceRecognitionSidebar {
            background-color: #1e2329;
            border-left: 2px solid #3a4149;
        }
        QLabel {
            color: white;
            font-family: Arial;
        }
        QListWidget {
            background-color: #2a3038;
            border: 1px solid #3a4149;
            border-radius: 5px;
        }
        QListWidget::item {
            border-bottom: 1px solid #3a4149;
            padding: 0px;
        }
        QListWidget::item:selected {
            background-color: #4a5058;
        }
    )");
    
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(15, 15, 15, 15);
    m_mainLayout->setSpacing(10);
    
    // Title
    m_titleLabel = new QLabel("DANH SÁCH NHẬN DIỆN");
    m_titleLabel->setFont(QFont("Arial", 14, QFont::Bold));
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setStyleSheet("color: white; padding: 10px; background-color: #3a4149; border-radius: 5px;");
    m_mainLayout->addWidget(m_titleLabel);
    
    // Count label
    m_countLabel = new QLabel("0 người");
    m_countLabel->setFont(QFont("Arial", 10));
    m_countLabel->setAlignment(Qt::AlignRight);
    m_countLabel->setStyleSheet("color: #888;");
    m_mainLayout->addWidget(m_countLabel);
    
    // Results list
    m_resultsList = new QListWidget();
    m_resultsList->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel);
    m_resultsList->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    connect(m_resultsList, &QListWidget::itemClicked, this, &FaceRecognitionSidebar::onFaceResultClicked);
    m_mainLayout->addWidget(m_resultsList);
    
    // Add some mock data for demonstration
    addFaceResult(FaceResult("Nguyễn Thái Quốc Huy", "95.5%", "Cho phép"));
    addFaceResult(FaceResult("Trịnh Quốc Bảo", "90.8%", "Cho phép"));
    addFaceResult(FaceResult("Trần Thành Long", "89.2%", "Từ chối"));
}

void FaceRecognitionSidebar::addFaceResult(const FaceResult& result)
{
    // Add to internal list
    m_faceResults.prepend(result); // Add to beginning for newest first
    
    // Limit the number of results
    while (m_faceResults.size() > m_maxResults) {
        m_faceResults.removeLast();
    }
    
    // Create widget for the result
    FaceResultWidget* resultWidget = new FaceResultWidget(result);
    
    // Create list item
    QListWidgetItem* item = new QListWidgetItem();
    item->setSizeHint(resultWidget->sizeHint());
    
    // Insert at the beginning
    m_resultsList->insertItem(0, item);
    m_resultsList->setItemWidget(item, resultWidget);
    
    // Remove excess items from the list widget
    while (m_resultsList->count() > m_maxResults) {
        QListWidgetItem* lastItem = m_resultsList->takeItem(m_resultsList->count() - 1);
        delete lastItem;
    }
    
    updateResultCount();
}

void FaceRecognitionSidebar::clearFaceResults()
{
    m_faceResults.clear();
    m_resultsList->clear();
    updateResultCount();
}

void FaceRecognitionSidebar::setMaxResults(int maxResults)
{
    m_maxResults = maxResults;
    
    // Remove excess results
    while (m_faceResults.size() > m_maxResults) {
        m_faceResults.removeLast();
    }
    
    while (m_resultsList->count() > m_maxResults) {
        QListWidgetItem* lastItem = m_resultsList->takeItem(m_resultsList->count() - 1);
        delete lastItem;
    }
    
    updateResultCount();
}

void FaceRecognitionSidebar::setShowTimestamps(bool show)
{
    m_showTimestamps = show;
    // Update all existing widgets
    for (int i = 0; i < m_resultsList->count(); ++i) {
        QListWidgetItem* item = m_resultsList->item(i);
        FaceResultWidget* widget = qobject_cast<FaceResultWidget*>(m_resultsList->itemWidget(item));
        if (widget) {
            widget->update();
        }
    }
}

void FaceRecognitionSidebar::setShowConfidence(bool show)
{
    m_showConfidence = show;
    // Update all existing widgets
    for (int i = 0; i < m_resultsList->count(); ++i) {
        QListWidgetItem* item = m_resultsList->item(i);
        FaceResultWidget* widget = qobject_cast<FaceResultWidget*>(m_resultsList->itemWidget(item));
        if (widget) {
            widget->update();
        }
    }
}

void FaceRecognitionSidebar::onCleanupOldResults()
{
    QDateTime cutoffTime = QDateTime::currentDateTime().addMSecs(-RESULT_TIMEOUT_MS);
    
    // Remove old results from internal list
    auto it = m_faceResults.begin();
    while (it != m_faceResults.end()) {
        QDateTime resultTime = QDateTime::fromString(it->timestamp, "hh:mm:ss");
        if (resultTime < cutoffTime) {
            it = m_faceResults.erase(it);
        } else {
            ++it;
        }
    }
    
    updateResultCount();
}

void FaceRecognitionSidebar::onFaceResultClicked(QListWidgetItem* item)
{
    FaceResultWidget* widget = qobject_cast<FaceResultWidget*>(m_resultsList->itemWidget(item));
    if (widget) {
        emit faceResultClicked(widget->getResult());
    }
}

void FaceRecognitionSidebar::updateResultCount()
{
    m_countLabel->setText(QString("%1 người").arg(m_faceResults.size()));
}

QPixmap FaceRecognitionSidebar::generateDefaultAvatar(const QString& name)
{
    QPixmap avatar(60, 60);
    avatar.fill(QColor(100, 150, 200));
    
    QPainter painter(&avatar);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 20, QFont::Bold));
    
    QString initials = name.isEmpty() ? "?" : QString(name.at(0).toUpper());
    painter.drawText(avatar.rect(), Qt::AlignCenter, initials);
    
    return avatar;
}
