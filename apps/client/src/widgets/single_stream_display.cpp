#include "widgets/single_stream_display.hpp"
#include <QApplication>
#include <QPainter>
#include <QFontMetrics>
#include <QDebug>
#include <QContextMenuEvent>
#include <QRandomGenerator>
#include <QtMath>
#include <cmath>

SingleStreamDisplay::SingleStreamDisplay(QWidget* parent)
    : QFrame(parent)
    , m_streamUrl("")
    , m_streamTitle("Camera 1 - 192.168.1.101")
    , m_isStreaming(false)
    , m_showFaceOverlays(true)
    , m_showStreamInfo(true)
    , m_showTimestamp(true)
    , m_mockFrameCounter(0)
    , m_mockBackgroundColor(QColor(40, 50, 60))
    , m_layout(nullptr)
    , m_streamInfoLabel(nullptr)
    , m_frameUpdateTimer(nullptr)
    , m_faceCleanupTimer(nullptr)
    , m_contextMenu(nullptr)
    , m_toggleAction(nullptr)
    , m_infoAction(nullptr)
    , m_fullscreenAction(nullptr)
{
    setupUI();
    setupContextMenu();
    
    // Setup timers
    m_frameUpdateTimer = new QTimer(this);
    connect(m_frameUpdateTimer, &QTimer::timeout, this, &SingleStreamDisplay::onUpdateFrame);
    
    m_faceCleanupTimer = new QTimer(this);
    connect(m_faceCleanupTimer, &QTimer::timeout, this, &SingleStreamDisplay::onCleanupOldFaces);
    m_faceCleanupTimer->start(FACE_CLEANUP_INTERVAL);
    
    // Start with mock stream
    startStream();
}

SingleStreamDisplay::~SingleStreamDisplay()
{
    if (m_frameUpdateTimer) {
        m_frameUpdateTimer->stop();
    }
    if (m_faceCleanupTimer) {
        m_faceCleanupTimer->stop();
    }
}

void SingleStreamDisplay::setupUI()
{
    setFrameStyle(QFrame::Box);
    setLineWidth(2);
    setStyleSheet(R"(
        SingleStreamDisplay {
            background-color: #1a1a1a;
            border: 2px solid #333;
        }
        QLabel {
            color: white;
            font-family: Arial;
        }
    )");
    
    setMinimumSize(640, 480);
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    
    m_layout = new QVBoxLayout(this);
    m_layout->setContentsMargins(0, 0, 0, 0);
    m_layout->setSpacing(0);
    
    // Stream info overlay (will be drawn in paintEvent)
    updateStreamInfo();
}

void SingleStreamDisplay::setupContextMenu()
{
    m_contextMenu = new QMenu(this);
    
    m_toggleAction = m_contextMenu->addAction("Toggle Stream");
    connect(m_toggleAction, &QAction::triggered, this, &SingleStreamDisplay::onToggleStream);
    
    m_infoAction = m_contextMenu->addAction("Stream Info");
    connect(m_infoAction, &QAction::triggered, this, &SingleStreamDisplay::onShowStreamInfo);
    
    m_contextMenu->addSeparator();
    
    m_fullscreenAction = m_contextMenu->addAction("Fullscreen");
    // connect(m_fullscreenAction, &QAction::triggered, this, &SingleStreamDisplay::onToggleFullscreen);
}

void SingleStreamDisplay::setStreamUrl(const QString& url)
{
    m_streamUrl = url;
    updateStreamInfo();
}

void SingleStreamDisplay::setStreamTitle(const QString& title)
{
    m_streamTitle = title;
    updateStreamInfo();
}

void SingleStreamDisplay::startStream()
{
    if (!m_isStreaming) {
        m_isStreaming = true;
        m_frameUpdateTimer->start(FRAME_UPDATE_INTERVAL);
        
        // Generate some initial mock faces
        generateMockFaces();
        
        qDebug() << "Started stream:" << m_streamTitle;
    }
}

void SingleStreamDisplay::stopStream()
{
    if (m_isStreaming) {
        m_isStreaming = false;
        m_frameUpdateTimer->stop();
        clearFaceOverlays();
        update();
        
        qDebug() << "Stopped stream:" << m_streamTitle;
    }
}

void SingleStreamDisplay::addFaceOverlay(const FaceOverlay& face)
{
    m_faceOverlays.append(face);
    
    // Limit the number of overlays
    while (m_faceOverlays.size() > 10) {
        m_faceOverlays.removeFirst();
    }
    
    update();
}

void SingleStreamDisplay::clearFaceOverlays()
{
    m_faceOverlays.clear();
    update();
}

void SingleStreamDisplay::setShowFaceOverlays(bool show)
{
    m_showFaceOverlays = show;
    update();
}

void SingleStreamDisplay::setShowStreamInfo(bool show)
{
    m_showStreamInfo = show;
    update();
}

void SingleStreamDisplay::setShowTimestamp(bool show)
{
    m_showTimestamp = show;
    update();
}

void SingleStreamDisplay::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Draw video frame
    drawVideoFrame(painter);
    
    if (m_isStreaming) {
        // Draw face overlays
        if (m_showFaceOverlays) {
            drawFaceOverlays(painter);
        }
        
        // Draw stream info
        if (m_showStreamInfo) {
            drawStreamInfo(painter);
        }
        
        // Draw timestamp
        if (m_showTimestamp) {
            drawTimestamp(painter);
        }
    }
}

void SingleStreamDisplay::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        emit streamClicked();
    }
    QFrame::mousePressEvent(event);
}

void SingleStreamDisplay::mouseDoubleClickEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        emit streamDoubleClicked();
    }
    QFrame::mouseDoubleClickEvent(event);
}

void SingleStreamDisplay::contextMenuEvent(QContextMenuEvent* event)
{
    if (m_contextMenu) {
        m_toggleAction->setText(m_isStreaming ? "Stop Stream" : "Start Stream");
        m_contextMenu->exec(event->globalPos());
    }
}

void SingleStreamDisplay::resizeEvent(QResizeEvent* event)
{
    QFrame::resizeEvent(event);
    // Regenerate frame to fit new size
    if (m_isStreaming) {
        generateMockFrame();
    }
}

void SingleStreamDisplay::onUpdateFrame()
{
    if (m_isStreaming) {
        generateMockFrame();
        
        // Occasionally update face positions
        if (m_mockFrameCounter % 90 == 0) { // Every 3 seconds at 30 FPS
            generateMockFaces();
        }
        
        update();
    }
}

void SingleStreamDisplay::onCleanupOldFaces()
{
    QDateTime cutoffTime = QDateTime::currentDateTime().addMSecs(-FACE_TIMEOUT_MS);
    
    auto it = m_faceOverlays.begin();
    while (it != m_faceOverlays.end()) {
        if (it->timestamp < cutoffTime) {
            it = m_faceOverlays.erase(it);
        } else {
            ++it;
        }
    }
    
    update();
}

void SingleStreamDisplay::onToggleStream()
{
    if (m_isStreaming) {
        stopStream();
    } else {
        startStream();
    }
}

void SingleStreamDisplay::onShowStreamInfo()
{
    // TODO: Show detailed stream information dialog
    qDebug() << "Stream Info:" << m_streamTitle << m_streamUrl;
}

void SingleStreamDisplay::generateMockFrame()
{
    QSize frameSize = size();
    if (frameSize.width() < 100 || frameSize.height() < 100) {
        frameSize = QSize(640, 480);
    }
    
    m_currentFrame = QPixmap(frameSize);
    m_currentFrame.fill(m_mockBackgroundColor);

    QPainter framePainter(&m_currentFrame);
    framePainter.setRenderHint(QPainter::Antialiasing);

    // Draw some animated elements to simulate video
    int time = m_mockFrameCounter++;

    // Moving circle
    int circleX = (time * 2) % frameSize.width();
    int circleY = frameSize.height() / 2 + static_cast<int>(100 * qSin(time * 0.1));
    framePainter.setBrush(QColor(100, 150, 255, 100));
    framePainter.setPen(Qt::NoPen);
    framePainter.drawEllipse(circleX - 20, circleY - 20, 40, 40);

    // Grid pattern
    framePainter.setPen(QPen(QColor(60, 80, 120), 1));
    for (int x = 0; x < frameSize.width(); x += 50) {
        framePainter.drawLine(x, 0, x, frameSize.height());
    }
    for (int y = 0; y < frameSize.height(); y += 50) {
        framePainter.drawLine(0, y, frameSize.width(), y);
    }

    // Simulate some office/indoor environment
    framePainter.setBrush(QColor(80, 60, 40, 150));
    framePainter.drawRect(50, frameSize.height() - 150, frameSize.width() - 100, 100); // Floor
    
    framePainter.setBrush(QColor(200, 200, 180, 100));
    framePainter.drawRect(frameSize.width() - 200, 50, 150, frameSize.height() - 200); // Wall
}

void SingleStreamDisplay::generateMockFaces()
{
    // Clear existing faces
    m_faceOverlays.clear();
    
    // Generate 1-3 random faces
    int faceCount = QRandomGenerator::global()->bounded(1, 4);
    QStringList names = {"Nguyễn Thái Quốc Huy", "Trịnh Quốc Bảo", "Trần Thành Long", "Unknown Person"};
    
    for (int i = 0; i < faceCount; ++i) {
        int x = QRandomGenerator::global()->bounded(50, width() - 150);
        int y = QRandomGenerator::global()->bounded(50, height() - 150);
        int w = QRandomGenerator::global()->bounded(80, 120);
        int h = QRandomGenerator::global()->bounded(100, 140);
        
        QString name = names[QRandomGenerator::global()->bounded(names.size())];
        QString confidence = QString("%1%").arg(QRandomGenerator::global()->bounded(75, 99));
        bool recognized = (name != "Unknown Person");
        
        FaceOverlay face(QRect(x, y, w, h), name, confidence, recognized);
        m_faceOverlays.append(face);
    }
}

void SingleStreamDisplay::drawVideoFrame(QPainter& painter)
{
    if (!m_currentFrame.isNull()) {
        // Scale frame to fit widget while maintaining aspect ratio
        QRect targetRect = rect();
        QPixmap scaledFrame = m_currentFrame.scaled(targetRect.size(), Qt::KeepAspectRatio, Qt::SmoothTransformation);

        // Center the frame
        int x = (targetRect.width() - scaledFrame.width()) / 2;
        int y = (targetRect.height() - scaledFrame.height()) / 2;

        painter.drawPixmap(x, y, scaledFrame);
    } else {
        // Draw placeholder
        painter.fillRect(rect(), QColor(30, 30, 30));
        painter.setPen(Qt::white);
        painter.setFont(QFont("Arial", 16));
        painter.drawText(rect(), Qt::AlignCenter, "No Video Signal");
    }
}

void SingleStreamDisplay::drawFaceOverlays(QPainter& painter)
{
    for (const auto& face : m_faceOverlays) {
        // Draw bounding box
        QColor boxColor = face.isRecognized ? QColor(0, 255, 0) : QColor(255, 100, 0);
        painter.setPen(QPen(boxColor, 3));
        painter.setBrush(Qt::NoBrush);
        painter.drawRect(face.boundingBox);

        // Draw name label background
        QFont labelFont("Arial", 12, QFont::Bold);
        painter.setFont(labelFont);
        QFontMetrics fm(labelFont);

        QString labelText = face.name.isEmpty() ? "Unknown" : face.name;
        if (!face.confidence.isEmpty()) {
            labelText += QString(" (%1)").arg(face.confidence);
        }

        QRect textRect = fm.boundingRect(labelText);
        textRect.adjust(-5, -2, 5, 2);
        textRect.moveTopLeft(QPoint(face.boundingBox.x(), face.boundingBox.y() - textRect.height() - 5));

        // Ensure label stays within widget bounds
        if (textRect.top() < 0) {
            textRect.moveTop(face.boundingBox.bottom() + 5);
        }
        if (textRect.right() > width()) {
            textRect.moveRight(width() - 5);
        }
        if (textRect.left() < 0) {
            textRect.moveLeft(5);
        }

        // Draw label background
        painter.setBrush(QColor(0, 0, 0, 180));
        painter.setPen(Qt::NoPen);
        painter.drawRect(textRect);

        // Draw label text
        painter.setPen(Qt::white);
        painter.drawText(textRect, Qt::AlignCenter, labelText);
    }
}

void SingleStreamDisplay::drawStreamInfo(QPainter& painter)
{
    // Draw stream info in top-left corner
    painter.setPen(Qt::white);
    painter.setBrush(QColor(0, 0, 0, 150));

    QFont infoFont("Arial", 11);
    painter.setFont(infoFont);

    QString infoText = m_streamTitle;
    if (!m_streamUrl.isEmpty()) {
        infoText += QString("\n%1").arg(m_streamUrl);
    }

    QFontMetrics fm(infoFont);
    QRect textRect = fm.boundingRect(QRect(0, 0, width() - 20, height()), Qt::TextWordWrap, infoText);
    textRect.adjust(-10, -5, 10, 5);
    textRect.moveTopLeft(QPoint(10, 10));

    painter.drawRect(textRect);
    painter.setPen(Qt::white);
    painter.drawText(textRect, Qt::TextWordWrap, infoText);
}

void SingleStreamDisplay::drawTimestamp(QPainter& painter)
{
    // Draw timestamp in bottom-left corner
    QString timestamp = QDateTime::currentDateTime().toString("dd/MM/yyyy hh:mm:ss");

    painter.setPen(Qt::white);
    painter.setBrush(QColor(0, 0, 0, 150));

    QFont timestampFont("Arial", 12, QFont::Bold);
    painter.setFont(timestampFont);

    QFontMetrics fm(timestampFont);
    QRect textRect = fm.boundingRect(timestamp);
    textRect.adjust(-10, -5, 10, 5);
    textRect.moveBottomLeft(QPoint(10, height() - 10));

    painter.drawRect(textRect);
    painter.setPen(Qt::white);
    painter.drawText(textRect, Qt::AlignCenter, timestamp);
}

void SingleStreamDisplay::updateStreamInfo()
{
    // This method can be used to update stream information
    // Currently just triggers a repaint
    update();
}
