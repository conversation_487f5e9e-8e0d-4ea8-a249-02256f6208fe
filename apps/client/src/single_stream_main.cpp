#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include "ui/single_stream_main_window.hpp"

int main(int argc, char* argv[])
{
    QApplication app(argc, argv);
    
    // Set application properties
    app.setApplicationName("C-AIBOX Face Recognition");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("C-AIBOX");
    app.setOrganizationDomain("c-aibox.com");
    
    // Set application icon (if available)
    // app.setWindowIcon(QIcon(":/icons/app_icon.png"));
    
    qDebug() << "Starting C-AIBOX Face Recognition System";
    qDebug() << "Qt Version:" << QT_VERSION_STR;
    qDebug() << "Application Dir:" << QDir::currentPath();
    
    try {
        // Create and show main window
        SingleStreamMainWindow window;
        window.show();
        
        qDebug() << "Main window created and displayed successfully";
        
        // Run the application
        int result = app.exec();
        
        qDebug() << "Application finished with code:" << result;
        return result;
        
    } catch (const std::exception& e) {
        qCritical() << "Exception caught:" << e.what();
        return -1;
    } catch (...) {
        qCritical() << "Unknown exception caught";
        return -1;
    }
}
