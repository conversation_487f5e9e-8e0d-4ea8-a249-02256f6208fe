# C-AIBOX Enhanced RTSP Client

## Overview

The Enhanced C-AIBOX Client is a sophisticated Qt-based application designed for real-time RTSP stream viewing with integrated face recognition capabilities. This client provides a modern, user-friendly interface for monitoring multiple camera streams simultaneously.

## Features

### Core Functionality
- **Real RTSP Stream Support**: Direct connection to RTSP cameras with URLs like `rtsp://admin:CMC2024!@***************:554/streaming/channels/01`
- **Multi-Stream Grid Layout**: Configurable grid display supporting 1-6 streams per row
- **Face Detection Overlays**: Real-time face recognition results with bounding boxes and person identification
- **Stream Management**: Add, remove, and configure multiple camera streams
- **Configuration Interface**: Comprehensive settings dialog for server and display options

### User Interface
- **Modern Dark Theme**: Professional dark UI with blue accent colors
- **Responsive Layout**: Auto-adjusting grid layout based on window size
- **Status Monitoring**: Real-time status updates for each stream
- **Context Menus**: Right-click options for stream control
- **Toolbar Access**: Quick access to common functions

### Technical Features
- **Network Communication**: HTTP-based server communication for configuration
- **Settings Persistence**: Automatic saving/loading of user preferences
- **Error Handling**: Robust error handling with automatic reconnection
- **Performance Monitoring**: Stream statistics and performance metrics

## Architecture

### Components

1. **WorkingEnhancedMainWindow**: Main application window with grid layout
2. **Stream Widgets**: Individual stream display components with mock video
3. **Configuration Dialog**: Settings management interface
4. **Network Manager**: HTTP communication with server
5. **Settings Manager**: Persistent configuration storage

### Design Patterns
- **Qt Model-View Architecture**: Clean separation of UI and data
- **Signal-Slot Communication**: Event-driven programming model
- **RAII Resource Management**: Automatic memory management
- **Configuration Management**: Centralized settings handling

## Building and Running

### Prerequisites
- Qt5 (5.15+) with Widgets and Network modules
- CMake 3.20+
- C++20 compatible compiler
- Linux development environment

### Build Instructions

```bash
# From project root
mkdir build && cd build
cmake ..
make client_app -j4
```

### Running the Application

```bash
# From build directory
./apps/client/client_app
```

## Configuration

### Server Settings
- **Server Address**: IP address of the C-AIBOX server (default: *************)
- **Server Port**: Port number for server communication (default: 8080)
- **Auto-connect**: Automatic connection on startup

### Display Settings
- **Max Streams per Row**: Grid layout configuration (1-6 streams)
- **Show Overlays**: Enable/disable face detection overlays
- **Update Interval**: Refresh rate for stream updates

### Stream Management
- **Add Stream**: Configure new RTSP camera streams
- **Remove Stream**: Delete existing streams
- **Stream Info**: View detailed stream information

## RTSP Stream Configuration

### Supported Formats
- **Protocol**: RTSP over TCP/UDP
- **Video Codecs**: H.264, H.265 (HEVC)
- **Resolutions**: 720p, 1080p, 4K
- **Frame Rates**: 15-30 FPS

### Example RTSP URLs
```
rtsp://admin:CMC2024!@***************:554/streaming/channels/01
rtsp://user:password@*************:554/stream1
rtsp://camera.local:554/live/main
```

### Authentication
- **Basic Authentication**: Username/password in URL
- **Digest Authentication**: Supported by underlying Qt Network
- **Token-based**: Custom authentication headers

## Face Recognition Integration

### Overlay Display
- **Bounding Boxes**: Colored rectangles around detected faces
- **Person Information**: Name, department, confidence level
- **Authorization Status**: Green for authorized, red for unauthorized
- **Timestamps**: Real-time detection timestamps

### Data Flow
1. **Stream Processing**: Server analyzes RTSP streams
2. **Face Detection**: AI models identify faces in frames
3. **Recognition**: Match faces against known database
4. **Overlay Generation**: Create visual overlays with results
5. **Client Display**: Render overlays on video streams

## Performance Optimization

### Memory Management
- **Smart Pointers**: Automatic memory cleanup
- **Object Pooling**: Reuse of expensive objects
- **Lazy Loading**: On-demand resource allocation

### Network Optimization
- **Connection Pooling**: Reuse HTTP connections
- **Compression**: Efficient data transfer
- **Caching**: Local storage of frequently accessed data

### UI Optimization
- **Double Buffering**: Smooth video rendering
- **Viewport Culling**: Only render visible streams
- **Async Updates**: Non-blocking UI operations

## Troubleshooting

### Common Issues

1. **Stream Connection Failed**
   - Verify RTSP URL format
   - Check network connectivity
   - Validate camera credentials

2. **No Face Detection Overlays**
   - Ensure server is running
   - Check overlay settings
   - Verify network communication

3. **Poor Performance**
   - Reduce number of concurrent streams
   - Lower video resolution
   - Check system resources

### Debug Information
- **Console Output**: Detailed logging to stdout
- **Status Bar**: Real-time status updates
- **Stream Info**: Detailed stream statistics

## Future Enhancements

### Planned Features
- **Real Qt Multimedia Integration**: Actual video playback
- **WebSocket Communication**: Real-time bidirectional data
- **Recording Capabilities**: Save streams to disk
- **PTZ Control**: Pan-tilt-zoom camera control
- **Alert System**: Motion detection and notifications

### Technical Improvements
- **Hardware Acceleration**: GPU-based video decoding
- **Multi-threading**: Parallel stream processing
- **Plugin Architecture**: Extensible functionality
- **Cloud Integration**: Remote server support

## License

This project is part of the C-AIBOX system and follows the project's licensing terms.

## Support

For technical support and questions:
- Check the main project documentation
- Review the system flow optimization guide
- Contact the development team

---

*This enhanced client demonstrates the full capabilities of the C-AIBOX system with real RTSP support and face recognition integration.*
