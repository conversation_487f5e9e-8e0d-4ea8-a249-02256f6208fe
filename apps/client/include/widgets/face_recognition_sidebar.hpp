#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QListWidget>
#include <QListWidgetItem>
#include <QFrame>
#include <QPixmap>
#include <QTimer>
#include <QDateTime>
#include <QPainter>
#include <QFont>

/**
 * @brief Face detection result for display
 */
struct FaceResult {
    QString name;
    QString confidence;
    QString timestamp;
    QString status; // "Cho phép" or "Từ chối"
    QPixmap avatar;

    FaceResult(const QString& n = "", const QString& c = "", const QString& s = "Cho phép")
        : name(n), confidence(c), timestamp(QDateTime::currentDateTime().toString("hh:mm:ss")), status(s) {}
};

/**
 * @brief Custom widget for displaying face recognition results
 */
class FaceResultWidget : public QWidget
{
    Q_OBJECT

public:
    explicit FaceResultWidget(const FaceResult& result, QWidget* parent = nullptr);
    
    void updateResult(const FaceResult& result);
    const FaceResult& getResult() const { return m_result; }

protected:
    void paintEvent(QPaintEvent* event) override;

private:
    FaceResult m_result;
    QPixmap m_scaledAvatar;
    
    void updateScaledAvatar();
};

/**
 * @brief Sidebar widget for displaying face recognition results
 * 
 * Shows a list of detected faces with their recognition status,
 * confidence levels, and timestamps.
 */
class FaceRecognitionSidebar : public QWidget
{
    Q_OBJECT

public:
    explicit FaceRecognitionSidebar(QWidget* parent = nullptr);
    ~FaceRecognitionSidebar();

    // Face result management
    void addFaceResult(const FaceResult& result);
    void clearFaceResults();
    void setMaxResults(int maxResults);

    // Display settings
    void setShowTimestamps(bool show);
    void setShowConfidence(bool show);

signals:
    void faceResultClicked(const FaceResult& result);

private slots:
    void onCleanupOldResults();
    void onFaceResultClicked(QListWidgetItem* item);

private:
    void setupUI();
    void updateResultCount();
    QPixmap generateDefaultAvatar(const QString& name);
    
    // UI Components
    QVBoxLayout* m_mainLayout;
    QLabel* m_titleLabel;
    QLabel* m_countLabel;
    QListWidget* m_resultsList;
    
    // Settings
    int m_maxResults;
    bool m_showTimestamps;
    bool m_showConfidence;
    
    // Data
    QList<FaceResult> m_faceResults;
    
    // Timer for cleanup
    QTimer* m_cleanupTimer;
    
    // Constants
    static const int DEFAULT_MAX_RESULTS = 50;
    static const int CLEANUP_INTERVAL_MS = 30000; // 30 seconds
    static const int RESULT_TIMEOUT_MS = 300000;  // 5 minutes
};
